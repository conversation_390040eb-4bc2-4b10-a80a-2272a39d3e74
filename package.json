{"name": "positive7-tourism-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate-types": "cross-env supabase gen types typescript --project-id $NEXT_PUBLIC_SUPABASE_PROJECT_ID --schema public > types/supabase.ts", "db:reset": "cross-env supabase db reset", "db:push": "cross-env supabase db push", "db:pull": "cross-env supabase db pull", "migrate:admin": "node ./scripts/migrate-admin-user.js", "postinstall": "node ./scripts/postinstall.js", "test:functionality": "node scripts/test-all-functionality.js", "clear-caches": "node scripts/clear-all-caches.js", "update-caching": "node scripts/update-caching-strategy.js"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.38.4", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-window": "^1.8.8", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "^10.0.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.0.0", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "framer-motion": "^11.0.0", "fs-extra": "^11.3.0", "googleapis": "^140.0.1", "lucide-react": "^0.294.0", "next": "^15.3.3", "next-cloudinary": "^6.16.0", "node-fetch": "^3.3.2", "nodemailer": "^6.9.9", "postcss": "^8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.3", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.0", "typescript": "^5", "web-vitals": "^5.0.1", "zod": "^3.25.46"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/bcryptjs": "^2.4.6", "@types/eslint": "^8", "@types/fs-extra": "^11.0.4", "eslint": "^8", "eslint-config-next": "^15.3.3", "sharp": "^0.34.2", "supabase": "^1.123.4"}, "engines": {"node": ">=22.0.0"}, "resolutions": {"react": "^18.2.0", "react-dom": "^18.2.0"}}