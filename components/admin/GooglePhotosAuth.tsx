'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { createClientSupabaseAuth } from '@/lib/auth';
import Button from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, LogOut, Camera, RefreshCw } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface GooglePhotosAuthProps {
  onAuthChange?: (isAuthenticated: boolean, accessToken?: string) => void;
  albumId?: string;
}

interface GoogleSession {
  access_token: string;
  expires_at: number;
  provider_token: string;
  user: {
    email: string;
    name: string;
  };
}

export default function GooglePhotosAuth({ onAuthChange, albumId }: GooglePhotosAuthProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [userInfo, setUserInfo] = useState<{ email: string; name: string } | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isCheckingSession, setIsCheckingSession] = useState(true);

  const supabase = createClientSupabaseAuth();

  const checkExistingSession = useCallback(async () => {
    try {
      setIsCheckingSession(true);
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.provider_token && session?.user) {
        // Check if this is a Google session with Photos scope
        const response = await fetch('https://www.googleapis.com/oauth2/v1/tokeninfo', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${session.provider_token}`,
          },
        });

        if (response.ok) {
          const tokenInfo = await response.json();
          if (tokenInfo.scope?.includes('photoslibrary')) {
            setIsAuthenticated(true);
            setAccessToken(session.provider_token);
            setUserInfo({
              email: session.user.email || '',
              name: session.user.user_metadata?.full_name || session.user.email || 'Google User'
            });
          }
        }
      }
    } catch (err) {
      console.error('Error checking existing session:', err);
    } finally {
      setIsCheckingSession(false);
    }
  }, [supabase]);

  // Check for existing Google session on component mount
  useEffect(() => {
    checkExistingSession();
  }, [checkExistingSession]);

  // Notify parent component when auth state changes
  useEffect(() => {
    onAuthChange?.(isAuthenticated, accessToken || undefined);
  }, [isAuthenticated, accessToken]); // Intentionally exclude onAuthChange to prevent infinite loops

  const signInWithGoogle = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          scopes: 'https://www.googleapis.com/auth/photoslibrary https://www.googleapis.com/auth/photoslibrary.sharing',
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
          redirectTo: `${window.location.origin}/admin/trips-photos`,
        },
      });

      if (error) {
        throw error;
      }

      // The redirect will happen automatically
    } catch (err: any) {
      console.error('Google sign-in error:', err);
      setError(err.message || 'Failed to sign in with Google');
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      await supabase.auth.signOut();
      setIsAuthenticated(false);
      setAccessToken(null);
      setUserInfo(null);
      setError(null);
    } catch (err: any) {
      console.error('Sign out error:', err);
      setError(err.message || 'Failed to sign out');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshToken = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        throw error;
      }

      if (data.session?.provider_token) {
        setAccessToken(data.session.provider_token);
      }
    } catch (err: any) {
      console.error('Token refresh error:', err);
      setError(err.message || 'Failed to refresh token');
    } finally {
      setIsLoading(false);
    }
  };

  if (isCheckingSession) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Checking authentication status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Camera className="h-5 w-5 text-blue-600" />
            <CardTitle>Google Photos Authentication</CardTitle>
          </div>
          <AnimatePresence>
            {isAuthenticated && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
              >
                <Badge variant="success" className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Connected
                </Badge>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        <CardDescription>
          {isAuthenticated 
            ? 'You are connected to Google Photos and can upload images to albums.'
            : 'Connect your Google account to upload images directly to Google Photos albums.'
          }
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start"
            >
              <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Authentication Error</p>
                <p className="text-sm">{error}</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {isAuthenticated && userInfo ? (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-green-800">Connected as:</p>
                  <p className="text-sm text-green-700">{userInfo.name}</p>
                  <p className="text-xs text-green-600">{userInfo.email}</p>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refreshToken}
                    disabled={isLoading}
                  >
                    <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={signOut}
                    disabled={isLoading}
                  >
                    <LogOut className="h-4 w-4 mr-1" />
                    Disconnect
                  </Button>
                </div>
              </div>
            </div>

            {albumId && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-700">
                  <strong>Ready to upload:</strong> Images will be uploaded to the Google Photos album.
                  Make sure the album is shared with your Google account.
                </p>
              </div>
            )}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-700">
                To upload images to Google Photos albums, you need to authenticate with your Google account.
                This will grant temporary access to upload images on your behalf.
              </p>
            </div>

            <Button
              onClick={signInWithGoogle}
              disabled={isLoading}
              variant="primary"
              size="md"
              className="w-full"
            >
              {isLoading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Camera className="h-4 w-4 mr-2" />
              )}
              Connect Google Photos
            </Button>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
