# Dependencies
/node_modules
/.pnp
.pnp.js
yarn.lock
package-lock.json
admin-credentials.txt

# Testing
/coverage

# Next.js
/.next/
/out/
/build

# Production
/build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local env files
.env*.local
.env
.env.development
.env.test
.env.production
.env.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE/Editor specific
.idea/
.vscode/
*.swp
*.swo
.cursor/

# OS specific
Thumbs.db
.DS_Store

# Logs
logs
*.log

# Cache directories
.npm
.eslintcache
.stylelintcache

# Misc
.cache
.turbo
dist

test