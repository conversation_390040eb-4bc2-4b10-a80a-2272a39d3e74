import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { extractFolderIdFromUrl } from '@/lib/google-drive';

// GET /api/admin/trips-photos - Get all trip photo details with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');

    // Build query
    let query = supabase
      .from('trip_photos_details')
      .select('*', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`trip_name.ilike.%${search}%,trip_description.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: tripPhotos, error, count } = await query;

    if (error) {
      console.error('Error fetching trip photos details:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trip photos details' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: tripPhotos || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/admin/trips-photos:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/trips-photos - Create a new trip photos detail
export async function POST(request: NextRequest) {
  try {
    const json = await request.json();
    
    const { trip_name, trip_description, featured_image_url, access_password, google_drive_link, storage_type } = json;
    
    if (!trip_name) {
      return NextResponse.json(
        { error: 'Trip name is required' },
        { status: 400 }
      );
    }
    
    // Extract folder ID from Google Drive link if provided
    const drive_folder_id = google_drive_link ? extractFolderIdFromUrl(google_drive_link) : null;
    
    const supabase = createServerSupabase();
    
    const { data, error } = await supabase
      .from('trip_photos_details')
      .insert([
        {
          trip_name,
          trip_description,
          featured_image_url,
          access_password,
          google_drive_link,
          drive_folder_id,
          storage_type
        }
      ])
      .select()
      .single();
    
    if (error) {
      throw error;
    }
    
    return NextResponse.json({ data });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An error occurred creating trip photo details' },
      { status: 500 }
    );
  }
} 