import { NextRequest, NextResponse } from 'next/server';
import sharp from 'sharp';
import fs from 'fs-extra';
import path from 'path';
import {
  uploadToGoogleDrive,
  uploadBufferToGoogleDrive,
  validateFolderAccess,
  isGoogleDriveLink,
  uploadToGooglePhotos,
  extractAlbumIdFromUrl
} from '@/lib/google-drive';
import { createServerSupabase } from '@/lib/supabase-server';

/**
 * Helper function to get the logo buffer, either from filesystem or by fetching from URL
 */
async function getLogoBuffer(): Promise<Buffer> {
  // Try local filesystem first (works in development)
  const logoPath = path.join(process.cwd(), 'public', 'images', 'positive7-logo.png');
  
  try {
    return await fs.readFile(logoPath);
  } catch (err) {
    console.log('Could not load logo from filesystem, trying URL fetch');
    
    // Determine the appropriate URL based on environment
    let logoUrl;
    if (process.env.VERCEL_URL) {
      // We're on Vercel
      logoUrl = `https://${process.env.VERCEL_URL}/images/positive7-logo.png`;
    } else if (process.env.NEXT_PUBLIC_SITE_URL) {
      // Custom domain is set
      logoUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/images/positive7-logo.png`;
    } else {
      // Fallback to localhost
      logoUrl = 'http://localhost:3000/images/positive7-logo.png';
    }
    
    console.log(`Fetching logo from: ${logoUrl}`);
    const response = await fetch(logoUrl);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch logo: ${response.statusText}`);
    }
    
    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
  }
}

export async function POST(req: NextRequest) {
  try {
    // Parse form data
    const formData = await req.formData();
    const imageFile = formData.get('image') as File;
    const tripPhotoDetailsId = formData.get('tripPhotoDetailsId') as string;

    if (!imageFile) {
      return NextResponse.json(
        { message: 'No image file provided' },
        { status: 400 }
      );
    }

    // Check file size (10MB max)
    const MAX_SIZE = 10 * 1024 * 1024; // 10MB
    if (imageFile.size > MAX_SIZE) {
      return NextResponse.json(
        { message: 'Image file too large. Maximum size is 10MB' },
        { status: 400 }
      );
    }

    // Check file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(imageFile.type)) {
      return NextResponse.json(
        { message: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are supported' },
        { status: 400 }
      );
    }

    // Get the buffer from the file
    const buffer = Buffer.from(await imageFile.arrayBuffer());

    // Get the logo buffer using our helper function
    const logoBuffer = await getLogoBuffer();
    
    // Get image dimensions
    const metadata = await sharp(buffer).metadata();
    const width = metadata.width || 800;
    
    // Resize logo to be proportional to the image width (15% of width)
    const logoWidth = Math.round(width * 0.15);
    const resizedLogo = await sharp(logoBuffer)
      .resize(logoWidth)
      .toBuffer();
    
    // Get resized logo metadata
    const logoMetadata = await sharp(resizedLogo).metadata();
    
    // Add watermark to top right with padding
    const padding = Math.round(width * 0.02); // 2% of width as padding
    
    // Process image with watermark
    const watermarkedBuffer = await sharp(buffer)
      .composite([
        {
          input: resizedLogo,
          top: padding,
          left: (width - (logoMetadata.width || 0)) - padding,
        },
      ])
      .toBuffer();

    // Generate unique filename
    const timestamp = Date.now();
    const ext = path.extname(imageFile.name) || '.jpg';
    const filename = `watermarked_${timestamp}${ext}`;
    
    // Instead of writing to filesystem, convert to base64 for response
    const base64Image = `data:${imageFile.type};base64,${watermarkedBuffer.toString('base64')}`;
    
    // Get the trip photo details to check for storage settings
    const supabase = createServerSupabase();
    const { data: tripPhotoDetails, error: fetchError } = await supabase
      .from('trip_photos_details')
      .select('google_drive_link, drive_folder_id, storage_type')
      .eq('id', tripPhotoDetailsId)
      .single();

    if (fetchError) {
      console.error('[WATERMARK] Error fetching trip photo details:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch trip photo details' },
        { status: 500 }
      );
    }

    // For Google Drive upload, we'll need to create a temporary file
    let driveUrl = null;
    
    if (tripPhotoDetails?.google_drive_link) {
      try {
        // Determine MIME type based on file extension
        const mimeType = 
          ext === '.png' ? 'image/png' :
          ext === '.gif' ? 'image/gif' :
          ext === '.webp' ? 'image/webp' :
          'image/jpeg'; // Default
          
        // Check if this is a Google Photos storage type
        if (tripPhotoDetails.storage_type === 'google_photos') {
          console.log(`[WATERMARK] Google Photos link detected, attempting to upload`);
          const albumId = extractAlbumIdFromUrl(tripPhotoDetails.google_drive_link) || tripPhotoDetails.google_drive_link;
          
          try {
            // For Google Photos, we need a temporary file
            const tempFilePath = `/tmp/${filename}`;
            await fs.writeFile(tempFilePath, watermarkedBuffer);
            
            // Upload to Google Photos
            await uploadToGooglePhotos(
              tempFilePath,
              filename,
              mimeType,
              albumId
            );
            console.log(`[WATERMARK] Uploaded to Google Photos successfully`);
            
            // Clean up temp file
            try {
              await fs.remove(tempFilePath);
            } catch (removeError) {
              console.error('[WATERMARK] Error removing temp file:', removeError);
            }
          } catch (photosError) {
            console.error('[WATERMARK] Error uploading to Google Photos:', photosError);
          }
        }
        // Check if this is a Google Drive link
        else if (isGoogleDriveLink(tripPhotoDetails.google_drive_link) && tripPhotoDetails.drive_folder_id) {
          console.log(`[WATERMARK] Checking access to Drive folder ID: ${tripPhotoDetails.drive_folder_id}`);
          // Validate folder access before attempting to upload
          const hasAccess = await validateFolderAccess(tripPhotoDetails.google_drive_link);
          
          if (hasAccess) {
            console.log(`[WATERMARK] Access validated, uploading to Drive`);
            
            // Use the buffer upload function instead of creating a temporary file
            driveUrl = await uploadBufferToGoogleDrive(
              watermarkedBuffer,
              filename,
              mimeType,
              tripPhotoDetails.drive_folder_id
            );
            console.log(`[WATERMARK] Uploaded to Drive successfully: ${driveUrl}`);
          } else {
            console.log(`[WATERMARK] No access to folder`);
          }
        }
      } catch (uploadError: any) {
        console.error('[WATERMARK] Error uploading to cloud storage:', uploadError);
        // We don't throw here - we'll return the base64 image if upload fails
      }
    }

    return NextResponse.json({
      message: 'Image processed successfully',
      imageUrl: base64Image,
      driveUrl,
      tripPhotoDetailsId
    });
    
  } catch (error: any) {
    console.error('Error processing image:', error);
    return NextResponse.json(
      { message: 'Failed to process image', error: error.message },
      { status: 500 }
    );
  }
} 