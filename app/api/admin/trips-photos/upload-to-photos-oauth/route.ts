import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth-helpers';
import { createServerSupabase } from '@/lib/supabase-server';
import { 
  uploadToGooglePhotosOAuth, 
  addToGooglePhotosAlbum, 
  extractGooglePhotosAlbumId,
  validateGooglePhotosAccess 
} from '@/lib/google-photos-oauth';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    // Check permissions
    if (!authResult.hasPermission?.('trips_photos', 'create')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const tripPhotoId = formData.get('tripPhotoId') as string;
    const accessToken = formData.get('accessToken') as string;

    if (!file || !tripPhotoId || !accessToken) {
      return NextResponse.json(
        { error: 'Missing required fields: file, tripPhotoId, or accessToken' },
        { status: 400 }
      );
    }

    console.log(`[PHOTOS_OAUTH_API] Starting upload process for trip photo ID: ${tripPhotoId}`);
    console.log(`[PHOTOS_OAUTH_API] File: ${file.name}, Size: ${file.size} bytes`);

    // Validate the access token has Google Photos permissions
    const hasValidAccess = await validateGooglePhotosAccess(accessToken);
    if (!hasValidAccess) {
      return NextResponse.json(
        { error: 'Invalid or insufficient Google Photos access. Please re-authenticate.' },
        { status: 401 }
      );
    }

    // Get trip photo details from database
    const supabase = createServerSupabase();
    const { data: tripPhotoDetails, error: fetchError } = await supabase
      .from('trip_photos_details')
      .select('*')
      .eq('id', tripPhotoId)
      .single();

    if (fetchError || !tripPhotoDetails) {
      console.error('[PHOTOS_OAUTH_API] Error fetching trip photo details:', fetchError);
      return NextResponse.json(
        { error: 'Trip photo details not found' },
        { status: 404 }
      );
    }

    // Check if this is a Google Photos album
    if (tripPhotoDetails.storage_type !== 'google_photos') {
      return NextResponse.json(
        { error: 'This trip is not configured for Google Photos uploads' },
        { status: 400 }
      );
    }

    // Extract album ID from the Google Photos URL
    const albumId = extractGooglePhotosAlbumId(tripPhotoDetails.google_drive_link);
    if (!albumId) {
      return NextResponse.json(
        { error: 'Could not extract album ID from Google Photos URL' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    try {
      // Upload to Google Photos
      const photoUrl = await uploadToGooglePhotosOAuth(
        buffer,
        file.name,
        file.type,
        accessToken
      );

      console.log(`[PHOTOS_OAUTH_API] Successfully uploaded to Google Photos: ${photoUrl}`);

      // If we have a specific album ID (not a shortened URL), try to add to album
      if (albumId && !albumId.includes('photos.app.goo.gl')) {
        try {
          // Extract media item ID from the photo URL if possible
          // This is a simplified approach - in practice, you might need to store the media item ID
          // from the upload response and use it here
          console.log(`[PHOTOS_OAUTH_API] Note: Album addition requires media item ID, which would be stored from upload response`);
        } catch (albumError) {
          console.warn('[PHOTOS_OAUTH_API] Could not add to specific album:', albumError);
          // Don't fail the entire upload if album addition fails
        }
      }

      // Update trip photo details in database
      const { error: updateError } = await supabase
        .from('trip_photos_details')
        .update({
          google_photos_url: photoUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', tripPhotoId);

      if (updateError) {
        console.error('[PHOTOS_OAUTH_API] Error updating database:', updateError);
        return NextResponse.json(
          { error: 'Upload successful but failed to update database' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Successfully uploaded to Google Photos',
        photoUrl: photoUrl,
        tripPhotoId: tripPhotoId
      });

    } catch (uploadError) {
      console.error('[PHOTOS_OAUTH_API] Google Photos upload failed:', uploadError);
      
      if (uploadError instanceof Error) {
        if (uploadError.message.includes('401') || uploadError.message.includes('403')) {
          return NextResponse.json(
            { error: 'Google Photos access denied. Please re-authenticate and ensure the album is accessible.' },
            { status: 401 }
          );
        }
        
        if (uploadError.message.includes('quota') || uploadError.message.includes('limit')) {
          return NextResponse.json(
            { error: 'Google Photos quota exceeded. Please try again later.' },
            { status: 429 }
          );
        }
      }

      return NextResponse.json(
        { error: 'Failed to upload to Google Photos', details: uploadError instanceof Error ? uploadError.message : 'Unknown error' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('[PHOTOS_OAUTH_API] Upload failed:', error);
    
    return NextResponse.json(
      { error: 'Upload failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
