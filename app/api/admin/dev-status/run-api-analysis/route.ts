import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth-helpers';
import fs from 'fs';
import path from 'path';

// Get all API routes
function getAllApiRoutes(dir = 'app/api', routes: any[] = []) {
  const fullDir = path.join(process.cwd(), dir);
  if (!fs.existsSync(fullDir)) return routes;

  const items = fs.readdirSync(fullDir);

  for (const item of items) {
    const fullPath = path.join(fullDir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      getAllApiRoutes(path.join(dir, item), routes);
    } else if (item === 'route.ts' || item === 'route.js') {
      // Convert file path to API route
      const apiPath = '/' + dir.replace('app/', '').replace(/\[([^\]]+)\]/g, ':$1');
      routes.push({
        path: apiPath,
        file: path.join(dir, item),
        methods: getHttpMethods(fullPath)
      });
    }
  }

  return routes;
}

// Extract HTTP methods from route file
function getHttpMethods(filePath: string) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const methods = [];

    if (content.includes('export async function GET')) methods.push('GET');
    if (content.includes('export async function POST')) methods.push('POST');
    if (content.includes('export async function PUT')) methods.push('PUT');
    if (content.includes('export async function DELETE')) methods.push('DELETE');
    if (content.includes('export async function PATCH')) methods.push('PATCH');

    return methods;
  } catch (error) {
    return [];
  }
}

// Search for API usage in codebase
function searchForApiUsage(apiPath: string, searchDirs = ['app', 'components', 'lib', 'hooks']) {
  const usages: any[] = [];

  function searchInDir(dir: string) {
    const fullDir = path.join(process.cwd(), dir);
    if (!fs.existsSync(fullDir)) return;

    const items = fs.readdirSync(fullDir);

    for (const item of items) {
      const fullPath = path.join(fullDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        searchInDir(path.join(dir, item));
      } else if (item.endsWith('.ts') || item.endsWith('.tsx') || item.endsWith('.js') || item.endsWith('.jsx')) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');

          // Clean API path for searching (remove parameters)
          const searchPath = apiPath.replace(/:\w+/g, '');

          // Look for various patterns
          const patterns = [
            new RegExp(`['"\`]${searchPath}['"\`]`, 'g'),
            new RegExp(`['"\`]/api${searchPath.replace('/api', '')}['"\`]`, 'g'),
            new RegExp(`fetch\\s*\\(\\s*['"\`][^'"\`]*${searchPath.replace('/api', '')}`, 'g'),
          ];

          for (const pattern of patterns) {
            const matches = content.match(pattern);
            if (matches) {
              usages.push({
                file: path.join(dir, item),
                matches: matches.length,
                patterns: matches
              });
              break; // Don't count multiple patterns in same file
            }
          }
        } catch (error) {
          // Skip files that can't be read
        }
      }
    }
  }

  for (const dir of searchDirs) {
    searchInDir(dir);
  }

  return usages;
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication and super admin role
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { adminUser } = authResult;
    const isSuperAdmin = adminUser?.roles?.some((role: any) => role.name === 'super_admin');
    
    if (!isSuperAdmin) {
      return NextResponse.json(
        { error: 'Super admin access required' },
        { status: 403 }
      );
    }



    const routes = getAllApiRoutes();
    const analysis = {
      total: routes.length,
      used: 0,
      unused: 0,
      suspicious: 0,
      routes: [] as any[]
    };
    
    for (const route of routes) {
      const usages = searchForApiUsage(route.path);
      const isUsed = usages.length > 0;
      
      // Check for suspicious patterns (placeholder responses)
      const fullPath = path.join(process.cwd(), route.file);
      let isSuspicious = false;
      try {
        const content = fs.readFileSync(fullPath, 'utf8');
        isSuspicious = content.includes('temporarily disabled') || 
                      content.includes('placeholder response') ||
                      content.includes('status: 501');
      } catch (error) {
        // Skip if can't read file
      }
      
      const routeAnalysis = {
        path: route.path,
        file: route.file,
        methods: route.methods,
        used: isUsed,
        suspicious: isSuspicious,
        usages: usages.length,
        usageFiles: usages.map(u => u.file)
      };
      
      analysis.routes.push(routeAnalysis);
      
      if (isUsed) {
        analysis.used++;
      } else {
        analysis.unused++;
      }
      
      if (isSuspicious) {
        analysis.suspicious++;
      }
    }

    // Generate recommendations
    const unusedRoutes = analysis.routes.filter(r => !r.used);
    const suspiciousRoutes = analysis.routes.filter(r => r.suspicious);
    
    const recommendations = [];
    
    if (unusedRoutes.length > 0) {
      recommendations.push({
        type: 'removal',
        priority: 'high',
        description: `Remove ${unusedRoutes.length} unused API routes`,
        routes: unusedRoutes.map(r => r.path)
      });
    }
    
    if (suspiciousRoutes.length > 0) {
      recommendations.push({
        type: 'review',
        priority: 'medium',
        description: `Review ${suspiciousRoutes.length} suspicious routes with placeholder responses`,
        routes: suspiciousRoutes.map(r => r.path)
      });
    }

    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: analysis.total,
        used: analysis.used,
        unused: analysis.unused,
        suspicious: analysis.suspicious,
        usageRate: ((analysis.used / analysis.total) * 100).toFixed(1)
      },
      routes: analysis.routes,
      recommendations
    };

    return NextResponse.json(report);

  } catch (error: any) {
    console.error('API analysis error:', error);
    return NextResponse.json(
      { error: 'Failed to run API analysis', details: error.message },
      { status: 500 }
    );
  }
}
