'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { TripPhotoDetails, TripPhotoDetailsFormData } from '@/types/trip-photos';
import EnhancedTripPhotoForm from '@/app/admin/trips-photos/components/EnhancedTripPhotoForm';

interface EditTripPhotoContentProps {
  tripPhotoDetails: TripPhotoDetails | null;
}

export default function EditTripPhotoContent({ tripPhotoDetails }: EditTripPhotoContentProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (formData: TripPhotoDetailsFormData) => {
    if (!tripPhotoDetails) return;

    const response = await fetch(`/api/admin/trips-photos/${tripPhotoDetails.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData),
    });

    if (!response.ok) {
      throw new Error('Failed to update trip photo album');
    }

    // Navigate back to detail page
    router.push(`/admin/trips-photos/${tripPhotoDetails.id}`);
  };

  const handleCancel = () => {
    router.push(`/admin/trips-photos/${tripPhotoDetails.id}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (!tripPhotoDetails) {
    return (
      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">Trip photo album not found</p>
            <button
              onClick={() => router.push('/admin/trips-photos')}
              className="mt-2 text-sm font-medium text-yellow-700 hover:text-yellow-600"
            >
              Back to Trip Photos
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <Link
          href={`/admin/trips-photos/${tripPhotoDetails.id}`}
          className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md flex items-center w-fit mb-4"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Album
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">Edit {tripPhotoDetails.trip_name}</h1>
      </div>

      <div className="bg-white shadow sm:rounded-lg p-6">
        <EnhancedTripPhotoForm
          initialData={{
            trip_name: tripPhotoDetails.trip_name,
            trip_description: tripPhotoDetails.trip_description,
            featured_image_url: tripPhotoDetails.featured_image_url,
            access_password: tripPhotoDetails.access_password,
            google_drive_link: tripPhotoDetails.google_drive_link,
            storage_type: tripPhotoDetails.storage_type,
          }}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
} 