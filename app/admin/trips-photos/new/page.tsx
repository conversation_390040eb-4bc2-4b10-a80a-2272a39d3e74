'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { TripPhotoDetailsFormData } from '@/types/trip-photos';
import AdminLayout from '@/components/layout/AdminLayout';
import EnhancedTripPhotoForm from '@/app/admin/trips-photos/components/EnhancedTripPhotoForm';
import { AlertCircle } from 'lucide-react';

export default function NewTripPhotoPage() {
  const router = useRouter();

  const handleSubmit = async (formData: TripPhotoDetailsFormData) => {
    const response = await fetch('/api/admin/trips-photos', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData),
    });

    if (!response.ok) {
      throw new Error('Failed to create trip photo album');
    }

    const data = await response.json();

    // Navigate to the newly created album
    router.push(`/admin/trips-photos/${data.data.id}`);
  };

  const handleCancel = () => {
    router.push('/admin/trips-photos');
  };

  return (
    <AdminLayout>
      <div className="mb-8">
        <Link
          href="/admin/trips-photos"
          className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md flex items-center w-fit mb-4"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Trip Photos
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">Create New Trip Photo Album</h1>
      </div>

      <div className="bg-white shadow sm:rounded-lg p-6">
        <EnhancedTripPhotoForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      </div>
    </AdminLayout>
  );
} 