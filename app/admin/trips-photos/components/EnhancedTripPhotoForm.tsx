'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { TripPhotoDetailsFormData, StorageType } from '@/types/trip-photos';
import { extractFolderIdFromUrl, extractGooglePhotosAlbumId } from '@/lib/url-utils';
import CloudinaryUpload from '@/components/ui/CloudinaryUpload';
import { useToast } from '@/hooks/useToast';
import { AlertCircle, HelpCircle, ExternalLink } from 'lucide-react';

interface EnhancedTripPhotoFormProps {
  initialData?: Partial<TripPhotoDetailsFormData>;
  onSubmit: (data: TripPhotoDetailsFormData) => Promise<void>;
  onCancel: () => void;
}

export default function EnhancedTripPhotoForm({
  initialData,
  onSubmit,
  onCancel,
}: EnhancedTripPhotoFormProps) {
  const toast = useToast();
  const [formData, setFormData] = useState<TripPhotoDetailsFormData>({
    trip_name: '',
    trip_description: '',
    featured_image_url: '',
    access_password: '',
    google_drive_link: '',
    storage_type: null,
    ...initialData,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [storageUrlError, setStorageUrlError] = useState<string | null>(null);

  const validateStorageUrl = (url: string, storageType: StorageType | null): string | null => {
    if (!url || !storageType) return null;

    if (storageType === 'google_drive') {
      if (!url.includes('drive.google.com/drive/folders/')) {
        return 'Please enter a valid Google Drive folder URL (https://drive.google.com/drive/folders/...)';
      }
      
      if (!extractFolderIdFromUrl(url)) {
        return 'Could not extract folder ID from the URL. Please use the format: https://drive.google.com/drive/folders/FOLDER_ID';
      }
    } else if (storageType === 'google_photos') {
      if (!url.includes('photos.google.com/albums') && !url.includes('photos.app.goo.gl')) {
        return 'Please enter a valid Google Photos album URL (https://photos.google.com/albums/... or https://photos.app.goo.gl/...)';
      }
    }

    return null;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    const newFormData = {
      ...formData,
      [name]: value === '' ? null : value
    };

    if (name === 'storage_type') {
      // Clear the URL when storage type changes
      newFormData.google_drive_link = '';
      setStorageUrlError(null);
    } else if (name === 'google_drive_link') {
      setStorageUrlError(validateStorageUrl(value, formData.storage_type));
    }
    
    setFormData(newFormData);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!formData.trip_name) {
      setError('Trip name is required');
      return;
    }

    if (formData.google_drive_link && !formData.storage_type) {
      setError('Please select a storage type when providing a storage URL');
      return;
    }

    if (formData.storage_type && !formData.google_drive_link) {
      setError('Please provide a storage URL when selecting a storage type');
      return;
    }

    if (storageUrlError) {
      setError('Please fix the storage URL error before submitting');
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStorageInstructions = () => {
    if (!formData.storage_type) return null;

    if (formData.storage_type === 'google_drive') {
      return (
        <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="text-sm font-medium text-blue-800 mb-2">Google Drive Setup Instructions:</h4>
          <ol className="text-xs text-blue-700 space-y-1 list-decimal list-inside">
            <li>Create a folder in Google Drive</li>
            <li>Share the folder with: <code className="bg-blue-100 px-1 rounded"><EMAIL></code></li>
            <li>Give "Editor" permissions to the service account</li>
            <li>Copy the folder URL and paste it below</li>
          </ol>
        </div>
      );
    } else if (formData.storage_type === 'google_photos') {
      return (
        <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
          <h4 className="text-sm font-medium text-green-800 mb-2">Google Photos Setup Instructions:</h4>
          <ol className="text-xs text-green-700 space-y-1 list-decimal list-inside">
            <li>Create an album in Google Photos</li>
            <li>Make sure the album is set to "Anyone with the link can add photos"</li>
            <li>Copy the album URL and paste it below</li>
            <li>Users will need to authenticate with Google to upload photos</li>
          </ol>
        </div>
      );
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <div>{error}</div>
        </div>
      )}

      <div>
        <label htmlFor="trip_name" className="block text-sm font-medium text-gray-700">
          Trip Name <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="trip_name"
          name="trip_name"
          value={formData.trip_name || ''}
          onChange={handleInputChange}
          required
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
        />
      </div>

      <div>
        <label htmlFor="trip_description" className="block text-sm font-medium text-gray-700">
          Trip Description
        </label>
        <textarea
          id="trip_description"
          name="trip_description"
          value={formData.trip_description || ''}
          onChange={handleInputChange}
          rows={3}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Featured Image
        </label>
        <CloudinaryUpload
          onUpload={(url) => setFormData(prev => ({ ...prev, featured_image_url: url }))}
          currentImage={formData.featured_image_url || undefined}
          uploadType="trip"
          placeholder="Upload trip featured image"
        />
      </div>

      <div>
        <label htmlFor="access_password" className="block text-sm font-medium text-gray-700">
          Access Password
        </label>
        <input
          type="password"
          id="access_password"
          name="access_password"
          value={formData.access_password || ''}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
          placeholder="Optional password for album access"
        />
        <p className="mt-1 text-xs text-gray-500">
          Leave empty for public access
        </p>
      </div>

      <div>
        <label htmlFor="storage_type" className="block text-sm font-medium text-gray-700">
          Storage Type
        </label>
        <select
          id="storage_type"
          name="storage_type"
          value={formData.storage_type || ''}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
        >
          <option value="">Select storage type...</option>
          <option value="google_drive">Google Drive</option>
          <option value="google_photos">Google Photos</option>
        </select>
        <p className="mt-1 text-xs text-gray-500">
          Choose where uploaded photos will be stored
        </p>
      </div>

      {formData.storage_type && (
        <div>
          <label htmlFor="google_drive_link" className="block text-sm font-medium text-gray-700">
            {formData.storage_type === 'google_drive' ? 'Google Drive Folder URL' : 'Google Photos Album URL'}
          </label>
          <input
            type="url"
            id="google_drive_link"
            name="google_drive_link"
            value={formData.google_drive_link || ''}
            onChange={handleInputChange}
            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 ${storageUrlError ? 'border-red-300' : ''}`}
            placeholder={
              formData.storage_type === 'google_drive' 
                ? 'https://drive.google.com/drive/folders/...' 
                : 'https://photos.google.com/albums/... or https://photos.app.goo.gl/...'
            }
          />
          
          {storageUrlError && (
            <p className="mt-1 text-sm text-red-600">
              {storageUrlError}
            </p>
          )}
          
          {formData.google_drive_link && !storageUrlError && formData.storage_type === 'google_drive' && (
            <p className="mt-1 text-sm text-green-600">
              Google Drive Folder ID: {extractFolderIdFromUrl(formData.google_drive_link)}
            </p>
          )}
          
          {formData.google_drive_link && !storageUrlError && formData.storage_type === 'google_photos' && (
            <p className="mt-1 text-sm text-green-600">
              Google Photos Album Link Valid
            </p>
          )}

          {getStorageInstructions()}
        </div>
      )}

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          {isSubmitting ? 'Saving...' : 'Save Trip Album'}
        </button>
      </div>
    </form>
  );
}
