import { NextRequest, NextResponse } from 'next/server';
import { createMiddlewareSupabaseAuth } from '@/lib/auth-server';

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const next = searchParams.get('next') ?? '/';

  if (code) {
    try {
      const { supabase, response } = createMiddlewareSupabaseAuth(request);
      
      // Exchange the code for a session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);
      
      if (error) {
        console.error('OAuth callback error:', error);
        return NextResponse.redirect(`${origin}/admin/login?error=oauth_error`);
      }

      if (data.session) {
        console.log('OAuth session established:', {
          provider: data.session.user.app_metadata?.provider,
          email: data.session.user.email,
          hasProviderToken: !!data.session.provider_token
        });

        // Redirect to the intended destination
        const redirectUrl = `${origin}${next}`;
        return NextResponse.redirect(redirectUrl);
      }
    } catch (error) {
      console.error('OAuth callback processing error:', error);
      return NextResponse.redirect(`${origin}/admin/login?error=callback_error`);
    }
  }

  // If no code or other error, redirect to login
  return NextResponse.redirect(`${origin}/admin/login?error=no_code`);
}
