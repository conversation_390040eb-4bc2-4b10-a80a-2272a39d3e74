/**
 * Migration script to populate storage_type field for existing trip_photos_details records
 * This script analyzes the google_drive_link field and sets the appropriate storage_type
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

function determineStorageType(googleDriveLink: string | null): 'google_drive' | 'google_photos' | null {
  if (!googleDriveLink) return null;
  
  // Check if it's a Google Photos link
  if (googleDriveLink.includes('photos.google.com') || googleDriveLink.includes('photos.app.goo.gl')) {
    return 'google_photos';
  }
  
  // Check if it's a Google Drive link
  if (googleDriveLink.includes('drive.google.com')) {
    return 'google_drive';
  }
  
  // Default to null if we can't determine
  return null;
}

async function migrateStorageTypes() {
  console.log('Starting storage type migration...');
  
  try {
    // Fetch all records that don't have storage_type set
    const { data: records, error: fetchError } = await supabase
      .from('trip_photos_details')
      .select('id, google_drive_link, storage_type')
      .is('storage_type', null);
    
    if (fetchError) {
      throw fetchError;
    }
    
    if (!records || records.length === 0) {
      console.log('No records found that need migration');
      return;
    }
    
    console.log(`Found ${records.length} records to migrate`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const record of records) {
      const storageType = determineStorageType(record.google_drive_link);
      
      if (storageType) {
        console.log(`Updating record ${record.id}: ${record.google_drive_link} -> ${storageType}`);
        
        const { error: updateError } = await supabase
          .from('trip_photos_details')
          .update({ storage_type: storageType })
          .eq('id', record.id);
        
        if (updateError) {
          console.error(`Error updating record ${record.id}:`, updateError);
          errorCount++;
        } else {
          successCount++;
        }
      } else {
        console.log(`Skipping record ${record.id}: Could not determine storage type for ${record.google_drive_link}`);
      }
    }
    
    console.log(`Migration completed: ${successCount} successful, ${errorCount} errors`);
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateStorageTypes()
  .then(() => {
    console.log('Migration script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
