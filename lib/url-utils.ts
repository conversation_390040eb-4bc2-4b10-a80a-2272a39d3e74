/**
 * Client-safe URL utility functions
 * These functions can be used in both client and server components
 */

/**
 * Extract folder ID from Google Drive URL
 * Works with various Google Drive URL formats
 */
export function extractFolderIdFromUrl(url: string): string | null {
  if (!url) return null;
  
  // Handle different Google Drive URL formats
  const patterns = [
    /\/folders\/([a-zA-Z0-9-_]+)/,
    /id=([a-zA-Z0-9-_]+)/,
    /\/d\/([a-zA-Z0-9-_]+)/
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }
  
  return null;
}

/**
 * Extract album ID from Google Photos URL
 * Works with various Google Photos URL formats
 */
export function extractGooglePhotosAlbumId(url: string): string | null {
  if (!url) return null;
  
  // Handle different Google Photos URL formats
  const patterns = [
    /\/albums\/([a-zA-Z0-9-_]+)/,
    /photos\.app\.goo\.gl\/([a-zA-Z0-9-_]+)/
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }
  
  return null;
}

/**
 * Check if URL is a Google Drive link
 */
export function isGoogleDriveLink(url: string): boolean {
  if (!url) return false;
  return url.includes('drive.google.com');
}

/**
 * Check if URL is a Google Photos link
 */
export function isGooglePhotosLink(url: string): boolean {
  if (!url) return false;
  return url.includes('photos.google.com') || url.includes('photos.app.goo.gl');
}

/**
 * Validate Google Drive folder URL format
 */
export function validateGoogleDriveUrl(url: string): { valid: boolean; message?: string } {
  if (!url) return { valid: false, message: 'URL is required' };
  
  if (!isGoogleDriveLink(url)) {
    return { valid: false, message: 'Must be a Google Drive URL' };
  }
  
  if (!url.includes('/folders/')) {
    return { valid: false, message: 'Must be a Google Drive folder URL' };
  }
  
  const folderId = extractFolderIdFromUrl(url);
  if (!folderId) {
    return { valid: false, message: 'Could not extract folder ID from URL' };
  }
  
  return { valid: true };
}

/**
 * Validate Google Photos album URL format
 */
export function validateGooglePhotosUrl(url: string): { valid: boolean; message?: string } {
  if (!url) return { valid: false, message: 'URL is required' };
  
  if (!isGooglePhotosLink(url)) {
    return { valid: false, message: 'Must be a Google Photos URL' };
  }
  
  const albumId = extractGooglePhotosAlbumId(url);
  if (!albumId) {
    return { valid: false, message: 'Could not extract album ID from URL' };
  }
  
  return { valid: true };
}
