/**
 * Google Photos API integration using OAuth tokens
 * This replaces the service account approach for Google Photos uploads
 */

interface GooglePhotosUploadResponse {
  uploadToken: string;
}

interface GooglePhotosMediaItem {
  id: string;
  productUrl: string;
  baseUrl: string;
  mimeType: string;
  filename: string;
}

interface GooglePhotosCreateResponse {
  newMediaItemResults: Array<{
    uploadToken: string;
    status: {
      message: string;
    };
    mediaItem: GooglePhotosMediaItem;
  }>;
}

/**
 * Upload a file buffer to Google Photos using OAuth access token
 */
export async function uploadToGooglePhotosOAuth(
  fileBuffer: Buffer,
  fileName: string,
  mimeType: string,
  accessToken: string
): Promise<string> {
  try {
    console.log(`[PHOTOS_OAUTH] Starting upload for file: ${fileName}`);
    console.log(`[PHOTOS_OAUTH] File size: ${fileBuffer.length} bytes`);
    console.log(`[PHOTOS_OAUTH] MIME type: ${mimeType}`);

    // Step 1: Upload the raw bytes to get an upload token
    const uploadResponse = await fetch('https://photoslibrary.googleapis.com/v1/uploads', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/octet-stream',
        'X-Goog-Upload-Content-Type': mimeType,
        'X-Goog-Upload-Protocol': 'raw',
      },
      body: fileBuffer,
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText} - ${errorText}`);
    }

    const uploadToken = await uploadResponse.text();
    console.log(`[PHOTOS_OAUTH] Upload token received: ${uploadToken.substring(0, 20)}...`);

    // Step 2: Create the media item in the user's library
    const createResponse = await fetch('https://photoslibrary.googleapis.com/v1/mediaItems:batchCreate', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        newMediaItems: [
          {
            description: `Uploaded via Positive7 Tourism - ${fileName}`,
            simpleMediaItem: {
              fileName: fileName,
              uploadToken: uploadToken,
            },
          },
        ],
      }),
    });

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      throw new Error(`Create media item failed: ${createResponse.status} ${createResponse.statusText} - ${errorText}`);
    }

    const createResult: GooglePhotosCreateResponse = await createResponse.json();
    console.log(`[PHOTOS_OAUTH] Create response:`, createResult);

    if (createResult.newMediaItemResults?.[0]?.mediaItem) {
      const mediaItem = createResult.newMediaItemResults[0].mediaItem;
      console.log(`[PHOTOS_OAUTH] Successfully created media item: ${mediaItem.id}`);
      return mediaItem.productUrl || mediaItem.baseUrl;
    } else {
      const status = createResult.newMediaItemResults?.[0]?.status;
      throw new Error(`Failed to create media item: ${status?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('[PHOTOS_OAUTH] Upload error:', error);
    throw new Error(`Failed to upload to Google Photos: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Add media items to a specific Google Photos album
 */
export async function addToGooglePhotosAlbum(
  mediaItemIds: string[],
  albumId: string,
  accessToken: string
): Promise<void> {
  try {
    console.log(`[PHOTOS_OAUTH] Adding ${mediaItemIds.length} items to album: ${albumId}`);

    const response = await fetch(`https://photoslibrary.googleapis.com/v1/albums/${albumId}:batchAddMediaItems`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        mediaItemIds: mediaItemIds,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Add to album failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    console.log(`[PHOTOS_OAUTH] Successfully added items to album`);
  } catch (error) {
    console.error('[PHOTOS_OAUTH] Add to album error:', error);
    throw new Error(`Failed to add to album: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get album information from Google Photos
 */
export async function getGooglePhotosAlbum(
  albumId: string,
  accessToken: string
): Promise<{ id: string; title: string; productUrl: string; isWriteable: boolean }> {
  try {
    console.log(`[PHOTOS_OAUTH] Getting album info: ${albumId}`);

    const response = await fetch(`https://photoslibrary.googleapis.com/v1/albums/${albumId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Get album failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const album = await response.json();
    console.log(`[PHOTOS_OAUTH] Album info retrieved:`, album);

    return {
      id: album.id,
      title: album.title,
      productUrl: album.productUrl,
      isWriteable: album.isWriteable || false,
    };
  } catch (error) {
    console.error('[PHOTOS_OAUTH] Get album error:', error);
    throw new Error(`Failed to get album info: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validate that the access token has the required Google Photos scopes
 */
export async function validateGooglePhotosAccess(accessToken: string): Promise<boolean> {
  try {
    console.log(`[PHOTOS_OAUTH] Validating access token`);

    const response = await fetch('https://www.googleapis.com/oauth2/v1/tokeninfo', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      return false;
    }

    const tokenInfo = await response.json();
    const hasPhotosScope = tokenInfo.scope?.includes('photoslibrary');
    
    console.log(`[PHOTOS_OAUTH] Token validation result: ${hasPhotosScope}`);
    return hasPhotosScope;
  } catch (error) {
    console.error('[PHOTOS_OAUTH] Token validation error:', error);
    return false;
  }
}

/**
 * Extract album ID from various Google Photos URL formats
 */
export function extractGooglePhotosAlbumId(url: string): string | null {
  if (!url) return null;
  
  // Handle different URL formats
  // Format: https://photos.google.com/albums/ALBUM_ID
  const albumMatch = url.match(/\/albums\/([^/?]+)/);
  if (albumMatch && albumMatch[1]) {
    return albumMatch[1];
  }
  
  // Format: https://photos.app.goo.gl/SHORTENED_ID (these need to be resolved)
  if (url.includes('photos.app.goo.gl/')) {
    // For shortened URLs, we'll return the full URL as we can't extract the ID reliably
    // The calling code should handle this case
    return url;
  }
  
  return null;
}

/**
 * Check if a URL is a Google Photos album link
 */
export function isGooglePhotosAlbumLink(url: string | null): boolean {
  if (!url) return false;
  return url.includes('photos.google.com/albums') || url.includes('photos.app.goo.gl');
}
